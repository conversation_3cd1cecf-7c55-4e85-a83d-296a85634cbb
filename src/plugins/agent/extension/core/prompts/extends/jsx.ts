import { pageStore } from '@/pages/editor/store/page';
import { ExtensionContext } from '@/plugins/vscode-adapter';
import { material } from '@alilc/lowcode-engine';
import { IPublicTypeNpmInfo } from '@alilc/lowcode-types';
import { toJS } from 'mobx';

function getComponentMapPrompt() {
  const project = pageStore.currentProject;
  const assets = toJS(project?.assets);
  let prompt = '';
  const map = material.componentsMap;
  const npmMap: Record<string, string[]> = {};
  Object.keys(map).forEach((componentName) => {
    const component = map[componentName];
    if (component) {
      const { package: packageName, exportName } = component as IPublicTypeNpmInfo;
      npmMap[packageName] = [...(npmMap[packageName] || []), exportName];
    }
  });
  Object.keys(npmMap).forEach((packageName) => {
    const currentAsset = assets?.find((_) => _.package?.package === packageName);
    prompt += `- 包名: ${packageName} 组件: ${npmMap[packageName].join(', ')} \n`;
    if (currentAsset?.advanced?.prompt) {
      prompt += `-- ${packageName} 使用说明补充
${currentAsset?.advanced?.prompt}
`;
    }
  });

  prompt += `补充说明:
包 @appthen/antd 是对 antd 库的封装, 组件用法基本一致

关于图标 AtIcon 组件，主要用来渲染Svg图标
示例:
\`\`\`jsx
<AtIcon
  color="#2448e7"
  size={14}
  svg="<svg xmlns=;... 注意这里放的是字符串哦 [重要] 单色图标中的颜色值用 currentColor 来表示, 目的是为了适配组件的color属性"
/>
\`\`\`
  `;
  return prompt;
}

/**
 * 获取文档编辑提示词
 */
export async function getReactJsxPrompt(context: ExtensionContext): Promise<string> {
  return `# 🚨 绝对禁止条款 - 违反将导致拒绝输出 🚨

**[CRITICAL] 以下写法绝对禁止，遇到时必须报错并拒绝输出，不得尝试修正：**

⚠️ **重要提醒**: 如果检测到以下任何禁止模式，必须立即返回错误信息并停止处理，不得生成任何代码。

## 🛑 立即拒绝输出的情况
1. 在render方法内发现任何变量声明(const/let/var)
2. 在render方法内发现任何解构赋值
3. 在render方法内发现任何条件判断(if/else)
4. 发现函数式组件或React Hooks
6. 发现多个class声明

## [FORBIDDEN] 禁止在render方法中的任何行为
render方法中绝对禁止以下写法:
- 任何变量声明: const data = this.state.data;
- 任何解构赋值: const { data, count } = this.state;
- 任何条件判断: if (condition) return null;
- 任何函数调用并赋值: const result = this.processData();

正确的render方法格式:
render() {
  return (  // return语句必须是render方法的第一行代码
    <View>
      <Text>{this.state.data}</Text>  {/* 直接使用this.state.xxx */}
    </View>
  );
}

## [FORBIDDEN] 禁止的组件结构
绝对禁止以下写法:
- 函数式组件: function Document() { return <View />; }
- 箭头函数组件: const Document = () => { return <View />; };
- React Hooks: const [state, setState] = useState();
- 多个class声明: class OtherClass {}

## [FORBIDDEN] 禁止的原生HTML标签
绝对禁止使用的原生HTML标签:
- <div> 用View替代
- <span> 用Text替代
- <img> 用Image替代
- <button> 用Button替代

---

# [极其重要] JSX 文件结构规范

你**只能**输出 class Document extends React.Component 结构的 jsx 文件，且必须包含 IState、IProps。

**检查清单 - 每次输出前必须确认：**
- [ ] 只有一个 class Document extends React.Component
- [ ] 包含 IState 和 IProps 定义
- [ ] render方法第一行就是return语句
- [ ] 没有使用任何const/let/var声明
- [ ] 没有使用不支持的原生HTML标签
- [ ] 没有使用函数式组件或Hooks

## JSX 文件完整示例

\`\`\`jsx
/*
 * 数据与接口请求定义   // [重要] 如果组件state中定义了字段，请在IState中定义对应的字段
 */
class IState {

  /* normal|warning|danger */
  tag?: string;

  /* 当前车辆 */
  currentTrain: any;

  /* 布尔类型 */
  showTrainDetail?: boolean;

  /* 查询车辆列表 */
  @DataSource({ // [重要] 这里提供对接口数据源的能力，非必选
    uri: this.globalData.get('BaseURL') + '/train/list' + \`?pageNum=1&pageSize=10\`,
    contentType: "JSON",
    method: "GET",
    params: { // 对应body参数
      data1: 'data1',
      data2: this.state.tag // [重要] 这里可以引用state中的字段
    },
    query: {
      pageNum: 1,
      pageSize: 10
    },
    dataHandler: res => { // [重要] 这里提供对接口数据处理的能力，非必选
      let data = res.data;
      return data;
    },
    isInit: false
  })
  trains?: string[]; // [重要] 数组类型请用[],不要用Array<类型>

  statusMap?: any; // [重要] 不确定对象类型请用any来表示

  /* 当前告警 */
  currentAlert: { // [重要] 对象类型请用{...}来表示
    /* 标题 */
    title: string;
    /* 内容 */
    content: string;
    /* 类型 */ // 对象的子字段也支持描述
    type: string;
  };
}

/*
 * 组件描述(仅在组件类型的jsx中才需要定义，根据需求定义)
 */
class IProps {
  /* 基本数据字段示例 */
  xxx?: string;
}

class Document extends React.Component { // [重要] 此行固定必须如此引入
  state = {
    tag: 'normal',
    showTrainDetail: false,
    trains: [],
    statusMap: {
      trains: { total: 0, normal: 0, warning: 0, error: 0 },
    },
    currentAlert: {
      createBy: '',
    },
  };

  /* 点击某某 */  // 函数方法根据情况可增加描述
  clickXxx() {
    this.setState({ count: this.state.count + 1 })
  }

  render() { // 渲染规范[核心]
    return ( // 严格遵守: render 方法中只能返回一个根节点，return 与 render() {之前不能有任何代码,比如 解构赋值、变量声明、逻辑运算
      <View>
        <Text>Hello World</Text>
      </View>
    );
  }
}
\`\`\`

### 🚨 [CRITICAL ERROR] 组件结构错误示例 - 必须拒绝输出

**如发现以下任何写法，立即报错并拒绝输出，不得继续处理：**

错误示例 - 立即拒绝输出：
- render方法内任何const/let/var声明
- render方法内任何解构赋值
- render方法内任何条件判断
- render方法内任何函数调用并赋值
- render方法内任何的console.log

检测到上述任一模式时的处理：
1. 立即停止代码生成
2. 返回错误信息："检测到禁止的写法，拒绝输出"
3. 不得尝试修正或继续

**✅ 唯一正确的写法：**
render方法第一行必须是return语句，直接使用this.state访问数据

### 5. 数据源使用规范

- 通过 this.dataSourceMap['dataSourceName']?.load() 调用数据源
- 支持 Promise 方式处理响应
- 数据源加载结果会自动更新到组件的 state 中
- 支持传入参数进行数据加载
- 支持链式调用和错误处理

### 6. 状态管理规范

- 仅支持使用 this.setState() 修改状态
- 状态定义在 state 对象中
- 不支持直接修改 state
- 不支持 Redux、Mobx 等状态管理库
- 不支持 React Hooks

## 二、组件定义规范

### 1. 组件类型

- 仅支持类组件(Class Component)定义
- 不支持函数组件(Function Component)
- 组件必须继承自 \`React.Component\`
- [重要] 不要使用 constructor 初始化状态、用componentDidMount代替
- 不要使用 privite/public

### 2. 组件结构

\`\`\`jsx
class Document extends React.Component {
  // 1. 组件状态定义
  state = {
    // 状态对象
  };

  /* 页面加载后执行 */
  componentDidMount() {
    // 组件挂载后执行
  }

  /* 自定义方法 */
  customMethod() {
    // 方法实现
  }

  // 4. 渲染方法
  render() {
    // 错误示例：不支持在render中声明变量 [重要][重要][重要][重要][重要][重要]
    const xxx = this.state.xxx;
    return <Component xxx={xxx} />;

    // 正确示例：直接使用this.state
    return <Component xxx={this.state.xxx} />;
  }
}
\`\`\`

### 3. 渲染方法约束

- render 方法中不支持声明变量
- 不支持在 render 方法中解构 state
- 不支持在 render 方法中声明常量
- 必须直接使用 this.state 访问状态
- 不支持在 render 方法中进行数据转换或计算
- 不支持在 render 方法中声明函数
- 不支持在 render 方法中使用 let/const 声明
- 不支持在 render 方法中使用 var 声明

## 三、数据管理规范

### 1. 状态管理

- 仅支持使用 \`this.setState()\` 修改状态
- 状态定义在 \`state\` 对象中
- 不支持 Redux、Mobx 等状态管理库
- 不支持 React Hooks

### 2. 数据更新方式

\`\`\`jsx
// 正确示例
this.setState({
  key: value,
});

// 错误示例
this.state.key = value; // 不支持直接修改
\`\`\`

### 3. 数据源使用

- 通过 \`this.dataSourceMap['dataSourceName']?.load()\` 调用数据源
- 支持 Promise 方式处理响应
- 数据源加载结果会自动更新到组件的 state 中

## 四、JSX 模板规范

### 1. 系统内置的组件

${getComponentMapPrompt()}

### 2. 样式规范

- 支持 Tailwind CSS 类名（尽可能多的使用tailwind css）
- 使用 className 定义样式
- 支持内联样式对象
- inlineStyle 属性仅支持 View 和 Text 组件使用
- 其他组件不支持 inlineStyle 属性

#### 2.1 Tailwind CSS 使用规范

- 使用 \`[]\` 语法指定自定义值,减少自定义类名

\`\`\`jsx
// 使用 [] 语法
className = 'w-[100px]'; // 自定义宽度
className = 'p-[10px]'; // 自定义内边距
className = 'text-[#333]'; // 自定义颜色
className = 'rounded-[6px]'; // 自定义圆角
\`\`\`

### 3. 事件处理

- 使用箭头函数绑定事件
- 事件处理函数中必须使用 this.setState 更新状态

### 4. 条件渲染

#### 4.1 JSX 模板中的条件渲染

\`\`\`jsx
// 正确示例：直接返回节点
{
  !!condition && <Component />;
}

// 正确示例：复杂条件表达式（一行）
{
  !!(condition1 && condition2 && condition3) && <Component />;
}

// 正确示例：条件渲染 + 循环渲染（一行）
{
  !!(this.state.form?.images?.length > 0) &&
    (this.state.form?.images).map((item, index) => (
      <View
        key={index}
        className="relative mb-[10px] w-[20%] flex flex-col justify-center items-center"
      >
        <Text>{item.name}</Text>
      </View>
    ));
}

// 错误示例：不支持先声明变量
const isVisible = !!condition;
{
  isVisible && <Component />;
}

// 错误示例：不支持多行写法
{
  condition && <Component />;
}
\`\`\`

#### 4.2 条件渲染约束

- JSX 模板中不支持使用三元表达式进行组件条件渲染
- 组件条件渲染推荐使用 \`!!\` 或 \`&&\` 运算符（注意不要额外生成什么额外的转义字符）
- 三元表达式仅可用于简单的属性值判断
- 不支持在条件渲染中使用函数调用
- 条件渲染的组件必须是静态的，不支持动态组件
- 条件渲染中不支持使用 this.setState
- 条件渲染必须使用单行写法，不支持多行写法
- 不支持先声明变量再进行条件渲染
- 条件渲染必须直接返回节点，不支持中间变量或复杂逻辑
- 支持复杂条件表达式，但必须保持单行写法
- 支持条件渲染和循环渲染的组合，但必须保持单行写法

#### 4.3 条件样式渲染

\`\`\`jsx
// 仅 View 和 Text 组件支持 inlineStyle
<View
  inlineStyle={[
    {
      enable: condition,
      name: '动态样式1',
      style: {
        backgroundColor: '#f1f1f1',
      },
    },
  ]}
  className="flex flex-row"
>
  <Text>内容</Text>
</View>
\`\`\`

#### 4.4 条件类名渲染

\`\`\`jsx
<View
  className={\`\${condition ? 'active' : ''} base-class\`}
  _unsafe_classNameJson={[
    {
      selector: 'active',
      condition: condition,
    },
  ]}
>
  <Text>内容</Text>
</View>
\`\`\`

### 5. 列表渲染

#### 5.1 基础列表渲染

\`\`\`jsx
// 正确示例：直接返回节点
{
  list.map((item, index) => (
    <View key={item.id}>
      <Text>{item.name}</Text>
    </View>
  ));
}

// 错误示例：不支持先声明变量
const items = list.map((item) => (
  <View key={item.id}>
    <Text>{item.name}</Text>
  </View>
));
{
  items;
}

// 错误示例：不支持多行写法
{
  list.map((item, index) => (
    <View key={item.id}>
      <Text>{item.name}</Text>
    </View>
  ));
}
\`\`\`

#### 5.2 带条件的列表渲染

\`\`\`jsx
// 正确示例：直接返回节点
{
  list.map((item, index) => (
    <View
      key={item.id}
      inlineStyle={[
        { enable: index % 2 === 0, name: '动态样式1', style: { backgroundColor: '#f1f1f1' } },
      ]}
    >
      <Text>{item.name}</Text>
    </View>
  ));
}

// 错误示例：不支持先声明变量
const items = list.map((item, index) => {
  const style = index % 2 === 0 ? { backgroundColor: '#f1f1f1' } : {};
  return (
    <View key={item.id} style={style}>
      <Text>{item.name}</Text>
    </View>
  );
});
{
  items;
}
\`\`\`

#### 5.3 列表渲染约束

- 必须提供唯一的 key 属性
- 不支持在列表渲染中使用 this.setState
- 列表项不支持动态组件
- 不支持嵌套的列表渲染
- 列表渲染必须使用单行写法，不支持多行写法
- 不支持在列表渲染中使用复杂的表达式或计算
- 不支持先声明变量再进行列表渲染
- 列表渲染必须直接返回节点，不支持中间变量或复杂逻辑

### 6. 组件参数渲染

#### 6.1 基础参数渲染

\`\`\`jsx
<Component title={title} children={children} />
\`\`\`

#### 6.2 带条件的参数渲染

\`\`\`jsx
<Component title={condition ? 'title1' : 'title2'} children={condition ? 'content1' : 'content2'} />
\`\`\`

#### 6.3 参数渲染约束

- 参数渲染必须使用单行写法，不支持多行写法
- 不支持在参数渲染中使用复杂的表达式或计算
- 不支持在参数渲染中使用函数调用
- 不支持在参数渲染中使用 this.setState
- 参数值必须是简单的表达式或常量

### 7. Render Props 支持

#### 7.1 基础 Render Props

\`\`\`jsx
<Component
  render={(data) => (
    <View>
      <Text>{data.name}</Text>
    </View>
  )}
/>
\`\`\`

#### 7.2 Render Props 约束

- render 函数必须是纯函数
- render 函数中不支持使用 this.setState
- render 函数中不支持使用生命周期方法
- render 函数中不支持使用 ref
- render 函数中不支持使用复杂的表达式或计算
- render 函数必须直接返回节点，不支持中间变量或复杂逻辑

### 8. 渲染约束

#### 8.1 条件渲染约束

- 不支持复杂的条件表达式组合
- 不支持在条件渲染中使用函数调用
- 条件渲染的组件必须是静态的,不支持动态组件
- 条件渲染中不支持使用 this.setState

#### 8.2 列表渲染约束

- 必须提供唯一的 key 属性
- 不支持在列表渲染中使用 this.setState
- 列表项不支持动态组件
- 不支持嵌套的列表渲染

#### 8.3 Render Props 约束

- render 函数必须是纯函数
- render 函数中不支持使用 this.setState
- render 函数中不支持使用生命周期方法
- render 函数中不支持使用 ref

### 9. 渲染性能优化

#### 9.1 条件渲染优化

\`\`\`jsx
// 正确示例：直接使用条件渲染
{
  !!condition && <Component />;
}

// 错误示例：不支持使用变量
const isVisible = !!condition;
{
  isVisible && <Component />;
}

// 错误示例：不支持复杂条件
{
  !!(condition1 && condition2 && condition3) && <Component />;
}
\`\`\`

#### 9.2 列表渲染优化

\`\`\`jsx
// 正确示例：直接使用 map 渲染
{
  list.map((item, index) => (
    <View key={item.id}>
      <Text>{item.name}</Text>
    </View>
  ));
}

// 错误示例：不支持使用变量
const items = list.map((item) => (
  <View key={item.id}>
    <Text>{item.name}</Text>
  </View>
));
{
  items;
}

// 错误示例：不支持复杂处理
{
  list.map((item) => {
    const processed = processItem(item);
    return <Component key={item.id} data={processed} />;
  });
}
\`\`\`

#### 9.3 渲染优化约束

- 不支持使用变量存储渲染结果
- 不支持使用 useMemo、useCallback 等 Hooks
- 不支持在渲染时进行复杂的数据处理
- 不支持使用中间变量
- 所有渲染必须直接返回节点
- 保持最基础的 React Class Component 写法

## 五、注释规范

### 1. 方法注释

- 使用 JSDoc 风格注释
- 注释用于可视化展示
- 必须包含方法功能说明

\`\`\`jsx
/*
 * 方法功能说明
 */
methodName() {
  // 方法实现
}
\`\`\`

## 六、工具方法

### 1. 内置工具

- this.utils.navigateTo: 页面跳转
- this.utils.navigateBack: 返回上一页
- this.utils.showToast: 显示提示
- this.utils.getFileUrl: 获取文件 URL
- this.utils.getRoute: 获取路由信息
- this.utils.selectImageUpload: 图片上传
- this.globalData.load: 重新加载全局数据

### 2. 数据源工具

- this.dataSourceMap: 数据源映射对象
- this.constants: 常量对象

## 七、限制说明

### 1. 不支持的特性

- 函数组件
- React Hooks
- Context API
- 自定义 Hooks
- 高阶组件
- 第三方状态管理
- 直接修改 state
- 异步组件
- 错误边界
- 严格模式

### 2. 性能考虑

- 避免在 render 中进行复杂计算
- 合理使用 shouldComponentUpdate
- 避免不必要的状态更新

[重要] 绝对不要在 render 里面写, 所有的组件引用state变量都得用完整路径 this.state.xxx.xx， 否则我们现在无法解析

---

# 🚨 强制执行规范检查 🚨

**在生成任何JSX代码前，必须执行以下检查：**

## 检查项目清单 (必须100%通过)
1. **render方法检查**: render() { 的下一行必须是 return (
2. **变量声明检查**: render方法内绝对不能有 const/let/var
3. **解构检查**: render方法内绝对不能有 { } =
4. **条件检查**: render方法内绝对不能有 if/else
5. **组件结构检查**: 只能有一个 class Document extends React.Component

## 发现违规时的处理流程
**步骤1**: 立即停止代码生成
**步骤2**: 返回错误消息："代码不符合平台规范，已拒绝输出"
**步骤3**: 不得尝试修正，不得提供替代方案
**步骤4**: 要求重新明确需求后再次生成

## 规范符合性验证
每次输出JSX代码前必须自问：
- ✅ render方法是否只有return语句？
- ✅ 是否直接使用this.state.xxx访问状态？
- ✅ 是否使用了平台支持的组件？
- ✅ 是否严格遵循class组件结构？

**只有100%符合规范才能输出代码，否则必须拒绝。**`;
}
