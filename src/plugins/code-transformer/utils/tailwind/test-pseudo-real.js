// 实际伪类冲突转换测试

import { EnhancedStyleTransformer } from './enhancedTailwindTransformer.js';

console.log('=== 实际伪类冲突转换测试 ===\n');

const realTestCases = [
  {
    name: '你的示例 - 背景色冲突',
    classes: [
      'hover:bg-blue-600', 'active:bg-blue-700', 'focus:ring-2', 'focus:ring-blue-300',
      'w-[200px]', 'h-[60px]', 'bg-[#3b82f6]', 'rounded-lg'
    ],
    description: 'bg-[#3b82f6] 应该保持为类名，避免覆盖 hover:bg-blue-600'
  },
  {
    name: '绿色按钮示例',
    classes: [
      'hover:bg-green-600', 'focus:outline-none', 'focus:ring-2', 'focus:ring-green-300',
      'focus:ring-opacity-50', 'w-[200px]', 'h-[60px]', 'bg-[#10b981]', 'rounded-lg'
    ],
    description: 'bg-[#10b981] 应该保持为类名，避免覆盖 hover:bg-green-600'
  },
  {
    name: '红色按钮示例',
    classes: [
      'hover:bg-red-600', 'active:bg-red-700', 'disabled:bg-red-300',
      'w-[200px]', 'h-[60px]', 'bg-[#ef4444]', 'rounded-lg'
    ],
    description: 'bg-[#ef4444] 应该保持为类名，避免覆盖 hover:bg-red-600'
  },
  {
    name: '无冲突示例',
    classes: ['w-[200px]', 'h-[60px]', 'bg-[#3b82f6]', 'rounded-lg', 'text-white'],
    description: '没有伪类，所有样式都应该转换为内联样式'
  }
];

console.log('🧪 实际转换测试:\n');

realTestCases.forEach((test, index) => {
  console.log(`${index + 1}. ${test.name}`);
  console.log(`   输入: ${test.classes.join(' ')}`);
  console.log(`   说明: ${test.description}`);
  
  try {
    const result = EnhancedStyleTransformer.toInlineStyle(test.classes);
    
    console.log(`   转换结果:`);
    console.log(`     内联样式: ${JSON.stringify(result.style, null, 2)}`);
    console.log(`     已转换类名: [${result.convertedClasses.join(', ')}]`);
    console.log(`     未转换类名: [${result.unconvertedClasses.join(', ')}]`);
    
    if (result.errors && result.errors.length > 0) {
      console.log(`     错误: ${result.errors.join(', ')}`);
    }
    
    console.log(`   状态: ✅ 转换完成\n`);
  } catch (error) {
    console.log(`   状态: ❌ 转换失败 - ${error.message}\n`);
  }
});

console.log('=== 期望的转换效果 ===\n');

console.log('📊 冲突检测预期:');
console.log('');

console.log('1. 有伪类冲突的情况:');
console.log('   输入: ["bg-[#3b82f6]", "hover:bg-blue-600", "w-[200px]"]');
console.log('   期望内联样式: { width: "200px" }');
console.log('   期望未转换: ["bg-[#3b82f6]", "hover:bg-blue-600"]');
console.log('   原因: bg-[#3b82f6] 与 hover:bg-blue-600 冲突，保持为类名');
console.log('');

console.log('2. 无伪类冲突的情况:');
console.log('   输入: ["bg-[#3b82f6]", "w-[200px]", "text-white"]');
console.log('   期望内联样式: { background: "#3b82f6", width: "200px", color: "#ffffff" }');
console.log('   期望未转换: []');
console.log('   原因: 没有伪类冲突，所有样式正常转换');
console.log('');

console.log('🎯 关键验证点:');
console.log('   • 冲突检测: 是否正确识别伪类与基础样式的冲突');
console.log('   • 保护机制: 冲突的基础样式是否保持为类名');
console.log('   • 正常转换: 无冲突的样式是否正常转换为内联样式');
console.log('   • 伪类保留: 所有伪类是否都保持为类名');

console.log('\n🚀 伪类冲突处理系统测试完成！');
